// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enum para tipos de usuário
enum UserRole {
  ADMIN
  TENANT
  CUSTOMER
  DELIVERY
}

// Enum para status de pedidos
enum OrderStatus {
  PENDING
  CONFIRMED
  PREPARING
  READY
  OUT_FOR_DELIVERY
  DELIVERED
  CANCELLED
}

// Enum para status de entrega
enum DeliveryStatus {
  AVAILABLE
  BUSY
  OFFLINE
}

// Enum para planos de assinatura
enum SubscriptionPlan {
  STARTER
  SMART
  PREMIUM
}

// Enum para status de assinatura
enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  EXPIRED
}

// Modelo de usuário base
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  phone     String?
  role      UserRole
  avatar    String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos específicos por role
  tenant   Tenant?
  customer Customer?
  delivery Delivery?

  // Relacionamentos de autenticação
  accounts Account[]
  sessions Session[]

  @@map("users")
}

// Modelo para lojistas (tenants)
model Tenant {
  id          String  @id @default(cuid())
  userId      String  @unique
  businessName String
  description String?
  logo        String?
  address     String
  latitude    Float?
  longitude   Float?
  isActive    Boolean @default(true)

  // Relacionamentos
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription Subscription?
  products     Product[]
  orders       Order[]
  categories   Category[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("tenants")
}

// Modelo para clientes
model Customer {
  id      String @id @default(cuid())
  userId  String @unique
  address String?

  // Relacionamentos
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("customers")
}

// Modelo para entregadores
model Delivery {
  id           String         @id @default(cuid())
  userId       String         @unique
  vehicle      String?
  license      String?
  rating       Float          @default(0)
  totalDeliveries Int         @default(0)
  status       DeliveryStatus @default(OFFLINE)
  latitude     Float?
  longitude    Float?

  // Relacionamentos
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("deliveries")
}

// Modelo de assinatura
model Subscription {
  id        String             @id @default(cuid())
  tenantId  String             @unique
  plan      SubscriptionPlan
  status    SubscriptionStatus @default(ACTIVE)
  price     Float
  startDate DateTime           @default(now())
  endDate   DateTime?

  // Limites do plano
  maxProducts Int
  maxOrders   Int

  // Relacionamentos
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("subscriptions")
}

// Modelo de categoria de produtos
model Category {
  id       String @id @default(cuid())
  tenantId String
  name     String
  isActive Boolean @default(true)

  // Relacionamentos
  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  products Product[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("categories")
}

// Modelo de produto
model Product {
  id          String  @id @default(cuid())
  tenantId    String
  categoryId  String?
  name        String
  description String?
  price       Float
  image       String?
  isActive    Boolean @default(true)
  stock       Int     @default(0)

  // Relacionamentos
  tenant    Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  category  Category?  @relation(fields: [categoryId], references: [id])
  orderItems OrderItem[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("products")
}

// Modelo de pedido
model Order {
  id         String      @id @default(cuid())
  tenantId   String
  customerId String
  deliveryId String?
  status     OrderStatus @default(PENDING)
  total      Float

  // Endereço de entrega
  deliveryAddress String
  deliveryLat     Float?
  deliveryLng     Float?

  // Informações de pagamento
  paymentMethod String?
  paymentStatus String?

  // Timestamps importantes
  orderDate    DateTime  @default(now())
  deliveryDate DateTime?

  // Relacionamentos
  tenant   Tenant      @relation(fields: [tenantId], references: [id])
  customer Customer    @relation(fields: [customerId], references: [id])
  delivery Delivery?   @relation(fields: [deliveryId], references: [id])
  items    OrderItem[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("orders")
}

// Modelo de item do pedido
model OrderItem {
  id        String @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Float

  // Relacionamentos
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Modelos para NextAuth.js
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}
