/**
 * @license lucide-react v0.454.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const HousePlus = createLucideIcon("HousePlus", [
  [
    "path",
    {
      d: "M13.22 2.416a2 2 0 0 0-2.511.057l-7 5.999A2 2 0 0 0 3 10v9a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7.354",
      key: "5phn05"
    }
  ],
  ["path", { d: "M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8", key: "5wwlr5" }],
  ["path", { d: "M15 6h6", key: "1jlkvy" }],
  ["path", { d: "M18 3v6", key: "x1uolp" }]
]);

export { HousePlus as default };
//# sourceMappingURL=house-plus.js.map
