"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

export default function Demo() {
  const [currentDemo, setCurrentDemo] = useState("tenant")

  const demoData = {
    tenant: {
      title: "Dashboard do Lojista",
      description: "Gerencie seus produtos, pedidos e vendas",
      features: [
        "📊 Dashboard com métricas em tempo real",
        "🛍️ Gestão de produtos e categorias",
        "📋 Acompanhamento de pedidos",
        "💰 Relatórios financeiros",
        "📱 Links de WhatsApp automáticos",
        "⚙️ Configurações da loja"
      ]
    },
    customer: {
      title: "App do Cliente",
      description: "Faça pedidos de forma rápida e fácil",
      features: [
        "🛒 Catálogo de produtos interativo",
        "🛍️ Carrinho de compras intuitivo",
        "📍 Rastreamento em tempo real",
        "💳 Múltiplas formas de pagamento",
        "⭐ Sistema de avaliações",
        "📱 Pedidos via WhatsApp"
      ]
    },
    delivery: {
      title: "App do Entregador",
      description: "Aceite entregas e ganhe dinheiro",
      features: [
        "📦 Lista de entregas disponíveis",
        "🗺️ Navegação GPS integrada",
        "💰 R$ 6,00 por entrega",
        "⭐ Sistema de avaliações",
        "📊 Histórico de entregas",
        "🎯 Metas e gamificação"
      ]
    },
    admin: {
      title: "Painel Administrativo",
      description: "Controle total da plataforma",
      features: [
        "👥 Gestão de usuários e tenants",
        "💳 Controle de assinaturas",
        "📊 Analytics da plataforma",
        "💰 Relatórios financeiros",
        "🔧 Configurações do sistema",
        "📈 Métricas de crescimento"
      ]
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                🚀 Delivery SaaS - Demo
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="outline">← Voltar</Button>
              </Link>
              <Link href="/auth/signup">
                <Button>Criar Conta</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Demo Navigation */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Explore as Funcionalidades
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Veja como cada tipo de usuário interage com a plataforma
          </p>
          
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Button
              variant={currentDemo === "tenant" ? "default" : "outline"}
              onClick={() => setCurrentDemo("tenant")}
            >
              👨‍💼 Lojista
            </Button>
            <Button
              variant={currentDemo === "customer" ? "default" : "outline"}
              onClick={() => setCurrentDemo("customer")}
            >
              👤 Cliente
            </Button>
            <Button
              variant={currentDemo === "delivery" ? "default" : "outline"}
              onClick={() => setCurrentDemo("delivery")}
            >
              🚚 Entregador
            </Button>
            <Button
              variant={currentDemo === "admin" ? "default" : "outline"}
              onClick={() => setCurrentDemo("admin")}
            >
              ⚙️ Admin
            </Button>
          </div>
        </div>

        {/* Demo Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">
                  {demoData[currentDemo as keyof typeof demoData].title}
                </CardTitle>
                <CardDescription className="text-lg">
                  {demoData[currentDemo as keyof typeof demoData].description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {demoData[currentDemo as keyof typeof demoData].features.map((feature, index) => (
                    <li key={index} className="flex items-center text-lg">
                      <span className="mr-3">{feature.split(' ')[0]}</span>
                      <span>{feature.substring(feature.indexOf(' ') + 1)}</span>
                    </li>
                  ))}
                </ul>
                
                <div className="mt-8 space-y-4">
                  <Button className="w-full" size="lg">
                    🎮 Testar Demo Interativa
                  </Button>
                  <Button variant="outline" className="w-full" size="lg">
                    📹 Assistir Vídeo Demo
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center mb-6">
              <div className="text-center">
                <div className="text-6xl mb-4">
                  {currentDemo === "tenant" && "👨‍💼"}
                  {currentDemo === "customer" && "👤"}
                  {currentDemo === "delivery" && "🚚"}
                  {currentDemo === "admin" && "⚙️"}
                </div>
                <p className="text-gray-500">
                  Preview do {demoData[currentDemo as keyof typeof demoData].title}
                </p>
              </div>
            </div>
            
            <div className="text-center">
              <p className="text-gray-600 mb-4">
                Interface intuitiva e responsiva para todos os dispositivos
              </p>
              <div className="flex justify-center space-x-4">
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                  📱 Mobile
                </span>
                <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                  💻 Desktop
                </span>
                <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
                  📱 PWA
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl">
                Pronto para começar?
              </CardTitle>
              <CardDescription className="text-lg">
                Crie sua conta gratuita e comece a usar todas essas funcionalidades hoje mesmo
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/signup">
                  <Button size="lg" className="w-full sm:w-auto">
                    🚀 Criar Conta Grátis
                  </Button>
                </Link>
                <Link href="/auth/signin">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Já tenho conta
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
