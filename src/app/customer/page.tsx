"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"

export default function CustomerDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [nearbyStores] = useState([
    { id: 1, name: "Pizzaria do João", rating: 4.8, deliveryTime: "25-35 min", deliveryFee: 5.90, category: "Pizza" },
    { id: 2, name: "Burger House", rating: 4.6, deliveryTime: "20-30 min", deliveryFee: 4.50, category: "Hambúrguer" },
    { id: 3, name: "Sushi Express", rating: 4.9, deliveryTime: "30-45 min", deliveryFee: 7.90, category: "Japonês" }
  ])

  const [recentOrders] = useState([
    { id: "#001", store: "Pizzaria do João", total: 45.90, status: "DELIVERED", date: "Hoje, 19:30" },
    { id: "#002", store: "Burger House", total: 32.50, status: "OUT_FOR_DELIVERY", date: "Hoje, 20:15" },
    { id: "#003", store: "Sushi Express", total: 67.80, status: "PREPARING", date: "Ontem, 18:45" }
  ])

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/auth/signin")
      return
    }

    if (session.user.role !== "CUSTOMER") {
      router.push("/dashboard")
      return
    }
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando...</p>
        </div>
      </div>
    )
  }

  if (!session || session.user.role !== "CUSTOMER") {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                🛒 Delivery SaaS
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Olá, {session.user.name}
              </span>
              <Button
                variant="outline"
                onClick={() => signOut({ callbackUrl: "/" })}
              >
                Sair
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Bem-vindo, {session.user.name}! 👋
          </h2>
          <p className="text-xl text-gray-600">
            O que você gostaria de pedir hoje?
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center">
              <div className="text-3xl mb-2">🍕</div>
              <p className="font-medium">Pizza</p>
            </CardContent>
          </Card>
          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center">
              <div className="text-3xl mb-2">🍔</div>
              <p className="font-medium">Hambúrguer</p>
            </CardContent>
          </Card>
          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center">
              <div className="text-3xl mb-2">🍣</div>
              <p className="font-medium">Japonês</p>
            </CardContent>
          </Card>
          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center">
              <div className="text-3xl mb-2">🥗</div>
              <p className="font-medium">Saudável</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Lojas Próximas */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>🏪 Lojas Próximas</CardTitle>
                <CardDescription>
                  Descubra os melhores restaurantes da sua região
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {nearbyStores.map((store) => (
                    <div key={store.id} className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                          <span className="text-2xl">🏪</span>
                        </div>
                        <div>
                          <h3 className="font-medium">{store.name}</h3>
                          <p className="text-sm text-gray-500">{store.category}</p>
                          <div className="flex items-center space-x-2 text-sm">
                            <span className="flex items-center">
                              ⭐ {store.rating}
                            </span>
                            <span>•</span>
                            <span>{store.deliveryTime}</span>
                            <span>•</span>
                            <span>Taxa: {formatCurrency(store.deliveryFee)}</span>
                          </div>
                        </div>
                      </div>
                      <Button variant="outline">
                        Ver Cardápio
                      </Button>
                    </div>
                  ))}
                </div>
                <Button className="w-full mt-4" variant="outline">
                  Ver Todas as Lojas
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div>
            {/* Pedidos Recentes */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>📋 Pedidos Recentes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium text-sm">{order.store}</p>
                          <p className="text-xs text-gray-500">{order.date}</p>
                        </div>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          order.status === "DELIVERED" ? "bg-green-100 text-green-800" :
                          order.status === "OUT_FOR_DELIVERY" ? "bg-blue-100 text-blue-800" :
                          "bg-yellow-100 text-yellow-800"
                        }`}>
                          {order.status === "DELIVERED" ? "Entregue" :
                           order.status === "OUT_FOR_DELIVERY" ? "A caminho" : "Preparando"}
                        </span>
                      </div>
                      <p className="text-sm font-medium">{formatCurrency(order.total)}</p>
                    </div>
                  ))}
                </div>
                <Button className="w-full mt-4" variant="outline">
                  Ver Histórico
                </Button>
              </CardContent>
            </Card>

            {/* Promoções */}
            <Card>
              <CardHeader>
                <CardTitle>🎉 Promoções</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="font-medium text-sm text-red-800">Frete Grátis</p>
                    <p className="text-xs text-red-600">Em pedidos acima de R$ 30</p>
                  </div>
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="font-medium text-sm text-blue-800">20% OFF</p>
                    <p className="text-xs text-blue-600">Na sua primeira pizza</p>
                  </div>
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p className="font-medium text-sm text-green-800">Cashback</p>
                    <p className="text-xs text-green-600">5% de volta em sushi</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
