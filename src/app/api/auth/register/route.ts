import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { UserRole, SubscriptionPlan, SubscriptionStatus } from '@prisma/client'
import { getSubscriptionLimits } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      name, 
      email, 
      phone, 
      password, 
      role, 
      businessName, 
      address 
    } = body

    // Validações básicas
    if (!name || !email || !password || !role) {
      return NextResponse.json(
        { error: 'Nome, email, senha e tipo de conta são obrigatórios' },
        { status: 400 }
      )
    }

    // Verificar se o email já existe
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Este email já está em uso' },
        { status: 400 }
      )
    }

    // Hash da senha
    const hashedPassword = await bcrypt.hash(password, 12)

    // Criar usuário
    const user = await prisma.user.create({
      data: {
        name,
        email,
        phone,
        role: role as UserRole,
        // Nota: Removemos o campo password do schema, mas você pode adicionar se necessário
      }
    })

    // Criar registros específicos baseado no role
    if (role === 'TENANT') {
      if (!businessName || !address) {
        return NextResponse.json(
          { error: 'Nome do negócio e endereço são obrigatórios para lojistas' },
          { status: 400 }
        )
      }

      // Criar tenant
      const tenant = await prisma.tenant.create({
        data: {
          userId: user.id,
          businessName,
          address
        }
      })

      // Criar assinatura padrão (Starter)
      const starterLimits = getSubscriptionLimits('STARTER')
      await prisma.subscription.create({
        data: {
          tenantId: tenant.id,
          plan: SubscriptionPlan.STARTER,
          status: SubscriptionStatus.ACTIVE,
          price: starterLimits.price,
          maxProducts: starterLimits.maxProducts,
          maxOrders: starterLimits.maxOrders
        }
      })
    } else if (role === 'CUSTOMER') {
      await prisma.customer.create({
        data: {
          userId: user.id,
          address: address || null
        }
      })
    } else if (role === 'DELIVERY') {
      await prisma.delivery.create({
        data: {
          userId: user.id
        }
      })
    }

    // Retornar sucesso (sem dados sensíveis)
    return NextResponse.json({
      message: 'Conta criada com sucesso',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Erro ao criar conta:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
