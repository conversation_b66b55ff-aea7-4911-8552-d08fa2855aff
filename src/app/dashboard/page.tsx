"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return // Ainda carregando

    if (!session) {
      router.push("/auth/signin")
      return
    }

    // Redirecionar para dashboard específico baseado no role
    if (session.user.role === "ADMIN") {
      router.push("/admin")
    } else if (session.user.role === "TENANT") {
      router.push("/tenant")
    } else if (session.user.role === "CUSTOMER") {
      router.push("/customer")
    } else if (session.user.role === "DELIVERY") {
      router.push("/delivery")
    }
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null // Será redirecionado
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                🚀 Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Olá, {session.user.name}
              </span>
              <Button
                variant="outline"
                onClick={() => signOut({ callbackUrl: "/" })}
              >
                Sair
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Bem-vindo ao Delivery SaaS!
          </h2>
          <p className="text-xl text-gray-600">
            Você está logado como: <span className="font-semibold">{session.user.role}</span>
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Card de Perfil */}
          <Card>
            <CardHeader>
              <CardTitle>👤 Meu Perfil</CardTitle>
              <CardDescription>
                Informações da sua conta
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>Nome:</strong> {session.user.name}</p>
                <p><strong>Email:</strong> {session.user.email}</p>
                <p><strong>Tipo:</strong> {session.user.role}</p>
              </div>
              <Button className="w-full mt-4" variant="outline">
                Editar Perfil
              </Button>
            </CardContent>
          </Card>

          {/* Card de Ações Rápidas */}
          <Card>
            <CardHeader>
              <CardTitle>⚡ Ações Rápidas</CardTitle>
              <CardDescription>
                Acesso rápido às principais funcionalidades
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {session.user.role === "TENANT" && (
                <>
                  <Button className="w-full" variant="outline">
                    📦 Gerenciar Produtos
                  </Button>
                  <Button className="w-full" variant="outline">
                    📋 Ver Pedidos
                  </Button>
                  <Button className="w-full" variant="outline">
                    📊 Relatórios
                  </Button>
                </>
              )}
              
              {session.user.role === "CUSTOMER" && (
                <>
                  <Button className="w-full" variant="outline">
                    🛒 Fazer Pedido
                  </Button>
                  <Button className="w-full" variant="outline">
                    📋 Meus Pedidos
                  </Button>
                  <Button className="w-full" variant="outline">
                    ⭐ Avaliações
                  </Button>
                </>
              )}
              
              {session.user.role === "DELIVERY" && (
                <>
                  <Button className="w-full" variant="outline">
                    📦 Entregas Disponíveis
                  </Button>
                  <Button className="w-full" variant="outline">
                    🗺️ Minhas Rotas
                  </Button>
                  <Button className="w-full" variant="outline">
                    💰 Ganhos
                  </Button>
                </>
              )}
              
              {session.user.role === "ADMIN" && (
                <>
                  <Button className="w-full" variant="outline">
                    👥 Gerenciar Usuários
                  </Button>
                  <Button className="w-full" variant="outline">
                    🏪 Gerenciar Tenants
                  </Button>
                  <Button className="w-full" variant="outline">
                    📊 Analytics
                  </Button>
                </>
              )}
            </CardContent>
          </Card>

          {/* Card de Estatísticas */}
          <Card>
            <CardHeader>
              <CardTitle>📊 Estatísticas</CardTitle>
              <CardDescription>
                Resumo das suas atividades
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Pedidos hoje:</span>
                  <span className="font-semibold">12</span>
                </div>
                <div className="flex justify-between">
                  <span>Total este mês:</span>
                  <span className="font-semibold">R$ 2.450,00</span>
                </div>
                <div className="flex justify-between">
                  <span>Avaliação média:</span>
                  <span className="font-semibold">⭐ 4.8</span>
                </div>
              </div>
              <Button className="w-full mt-4" variant="outline">
                Ver Detalhes
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Seção de Desenvolvimento */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>🚧 Em Desenvolvimento</CardTitle>
            <CardDescription>
              Esta é uma versão demo. As funcionalidades completas estão sendo desenvolvidas.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">✅ Já Implementado:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Sistema de autenticação</li>
                  <li>• Multitenancy</li>
                  <li>• APIs básicas</li>
                  <li>• Interface responsiva</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">🔄 Próximas Features:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Dashboard completo</li>
                  <li>• Gestão de produtos</li>
                  <li>• Sistema de pedidos</li>
                  <li>• Integração WhatsApp</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
