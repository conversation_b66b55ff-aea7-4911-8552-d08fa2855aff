"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"

export default function DeliveryDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isOnline, setIsOnline] = useState(false)
  const [stats] = useState({
    todayEarnings: 48.00,
    todayDeliveries: 8,
    monthlyEarnings: 1440.00,
    totalDeliveries: 240,
    rating: 4.8,
    completionRate: 98
  })

  const [availableOrders] = useState([
    { 
      id: "#001", 
      store: "Pizzaria do João", 
      customer: "Maria Silva", 
      distance: "1.2 km", 
      payment: 6.00,
      pickup: "R<PERSON> das Flores, 123",
      delivery: "Av. Principal, 456"
    },
    { 
      id: "#002", 
      store: "Burger House", 
      customer: "João Santos", 
      distance: "0.8 km", 
      payment: 6.00,
      pickup: "Rua do Comércio, 789",
      delivery: "R<PERSON> das Palmeiras, 321"
    }
  ])

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/auth/signin")
      return
    }

    if (session.user.role !== "DELIVERY") {
      router.push("/dashboard")
      return
    }
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando...</p>
        </div>
      </div>
    )
  }

  if (!session || session.user.role !== "DELIVERY") {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                🚚 Entregador
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Status:</span>
                <Button
                  variant={isOnline ? "default" : "outline"}
                  size="sm"
                  onClick={() => setIsOnline(!isOnline)}
                >
                  {isOnline ? "🟢 Online" : "🔴 Offline"}
                </Button>
              </div>
              <span className="text-sm text-gray-600">
                {session.user.name}
              </span>
              <Button
                variant="outline"
                onClick={() => signOut({ callbackUrl: "/" })}
              >
                Sair
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ganhos Hoje</CardTitle>
              <span className="text-2xl">💰</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.todayEarnings)}</div>
              <p className="text-xs text-muted-foreground">
                {stats.todayDeliveries} entregas
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ganhos Mensais</CardTitle>
              <span className="text-2xl">📊</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.monthlyEarnings)}</div>
              <p className="text-xs text-muted-foreground">
                {stats.totalDeliveries} entregas totais
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avaliação</CardTitle>
              <span className="text-2xl">⭐</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.rating}</div>
              <p className="text-xs text-muted-foreground">
                Baseado em 89 avaliações
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Taxa de Conclusão</CardTitle>
              <span className="text-2xl">✅</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completionRate}%</div>
              <p className="text-xs text-muted-foreground">
                Últimos 30 dias
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Entregas Disponíveis */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>📦 Entregas Disponíveis</CardTitle>
                <CardDescription>
                  {isOnline ? "Aceite entregas e comece a ganhar!" : "Fique online para ver entregas disponíveis"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!isOnline ? (
                  <div className="text-center py-8">
                    <div className="text-6xl mb-4">🔴</div>
                    <p className="text-gray-500 mb-4">Você está offline</p>
                    <Button onClick={() => setIsOnline(true)}>
                      Ficar Online
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {availableOrders.map((order) => (
                      <div key={order.id} className="p-4 border rounded-lg">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h3 className="font-medium">{order.id} - {order.store}</h3>
                            <p className="text-sm text-gray-500">Cliente: {order.customer}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-green-600">{formatCurrency(order.payment)}</p>
                            <p className="text-sm text-gray-500">{order.distance}</p>
                          </div>
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium">Retirada:</span>
                            <p className="text-gray-600">{order.pickup}</p>
                          </div>
                          <div>
                            <span className="font-medium">Entrega:</span>
                            <p className="text-gray-600">{order.delivery}</p>
                          </div>
                        </div>

                        <div className="flex space-x-2 mt-4">
                          <Button className="flex-1" variant="outline">
                            Ver Rota
                          </Button>
                          <Button className="flex-1">
                            Aceitar Entrega
                          </Button>
                        </div>
                      </div>
                    ))}
                    
                    {availableOrders.length === 0 && (
                      <div className="text-center py-8">
                        <div className="text-6xl mb-4">📦</div>
                        <p className="text-gray-500">Nenhuma entrega disponível no momento</p>
                        <p className="text-sm text-gray-400 mt-2">Novas entregas aparecerão aqui automaticamente</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div>
            {/* Metas Diárias */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>🎯 Metas Diárias</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Entregas (8/12)</span>
                      <span>67%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '67%' }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Ganhos (R$ 48/R$ 72)</span>
                      <span>67%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '67%' }}></div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-800">🏆 Bônus Disponível</p>
                  <p className="text-xs text-blue-600">Complete 4 entregas e ganhe +R$ 10</p>
                </div>
              </CardContent>
            </Card>

            {/* Ações Rápidas */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>⚡ Ações Rápidas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" variant="outline">
                  📍 Atualizar Localização
                </Button>
                <Button className="w-full" variant="outline">
                  📊 Ver Histórico
                </Button>
                <Button className="w-full" variant="outline">
                  💰 Relatório de Ganhos
                </Button>
                <Button className="w-full" variant="outline">
                  ⚙️ Configurações
                </Button>
              </CardContent>
            </Card>

            {/* Ranking */}
            <Card>
              <CardHeader>
                <CardTitle>🏆 Ranking Semanal</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-600 mb-2">#3</div>
                  <p className="text-sm text-gray-600 mb-4">Você está em 3º lugar!</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>🥇 Carlos Silva</span>
                      <span>45 entregas</span>
                    </div>
                    <div className="flex justify-between">
                      <span>🥈 Ana Costa</span>
                      <span>42 entregas</span>
                    </div>
                    <div className="flex justify-between font-medium">
                      <span>🥉 Você</span>
                      <span>38 entregas</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
