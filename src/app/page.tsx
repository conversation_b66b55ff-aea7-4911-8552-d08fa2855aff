"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Link from "next/link"

export default function Home() {
  const { data: session } = useSession()
  const router = useRouter()

  const handleGetStarted = () => {
    if (session) {
      // Redirecionar baseado no role do usuário
      if (session.user.role === "ADMIN") {
        router.push("/admin")
      } else if (session.user.role === "TENANT") {
        router.push("/tenant")
      } else if (session.user.role === "CUSTOMER") {
        router.push("/customer")
      } else if (session.user.role === "DELIVERY") {
        router.push("/delivery")
      } else {
        router.push("/dashboard")
      }
    } else {
      router.push("/auth/signup")
    }
  }

  const handleSignIn = () => {
    router.push("/auth/signin")
  }

  const handleDemo = () => {
    router.push("/demo")
  }

  const handlePlanSelect = (planName: string) => {
    if (session) {
      router.push(`/subscription?plan=${planName.toLowerCase()}`)
    } else {
      router.push(`/auth/signup?plan=${planName.toLowerCase()}`)
    }
  }

  const plans = [
    {
      name: "Starter",
      price: 19.90,
      description: "Perfeito para começar seu negócio",
      features: [
        "10 produtos",
        "500 pedidos/mês",
        "Suporte básico",
        "Dashboard básico"
      ]
    },
    {
      name: "Smart",
      price: 49.90,
      description: "Para negócios em crescimento",
      features: [
        "30 produtos",
        "1.500 pedidos/mês",
        "Suporte prioritário",
        "Analytics avançado",
        "Integração WhatsApp"
      ]
    },
    {
      name: "Premium",
      price: 99.90,
      description: "Para empresas estabelecidas",
      features: [
        "100 produtos",
        "Pedidos ilimitados",
        "Suporte 24/7",
        "Analytics completo",
        "Integração WhatsApp",
        "API personalizada",
        "Relatórios avançados"
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                🚀 Delivery SaaS
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {session ? (
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    Olá, {session.user.name}
                  </span>
                  <Button onClick={() => router.push("/dashboard")}>
                    Dashboard
                  </Button>
                </div>
              ) : (
                <>
                  <Button variant="outline" onClick={handleSignIn}>
                    Entrar
                  </Button>
                  <Button onClick={handleGetStarted}>
                    Começar Grátis
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Transforme seu negócio em uma
            <span className="text-blue-600"> plataforma de delivery</span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Sistema completo de delivery multitenancy com pedidos via WhatsApp,
            rastreamento em tempo real, gamificação e muito mais.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="text-lg px-8 py-4"
              onClick={handleGetStarted}
            >
              {session ? "Ir para Dashboard" : "Começar Agora - Grátis"}
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-4"
              onClick={handleDemo}
            >
              Ver Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Funcionalidades Diferenciais
            </h3>
            <p className="text-xl text-gray-600">
              Tudo que você precisa para dominar o mercado de delivery
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📱",
                title: "Pedidos via WhatsApp",
                description: "Gere links de checkout direto para WhatsApp"
              },
              {
                icon: "🗺️",
                title: "Rastreamento Real-time",
                description: "Acompanhe entregadores como no Uber"
              },
              {
                icon: "🎮",
                title: "Gamificação",
                description: "Selos, desafios e rankings para engajar"
              },
              {
                icon: "🌱",
                title: "Impacto Social",
                description: "Dashboard mostra negócios locais apoiados"
              },
              {
                icon: "🤖",
                title: "IA Integrada",
                description: "Atendimento automatizado inteligente"
              },
              {
                icon: "📊",
                title: "Analytics Avançado",
                description: "Insights e sugestões automáticas"
              }
            ].map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Planos que Crescem com Você
            </h3>
            <p className="text-xl text-gray-600">
              Escolha o plano ideal para o seu negócio
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <Card key={index} className={`relative ${index === 1 ? 'border-blue-500 border-2' : ''}`}>
                {index === 1 && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Mais Popular
                    </span>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="text-4xl font-bold text-blue-600 my-4">
                    {formatCurrency(plan.price)}
                    <span className="text-lg text-gray-500 font-normal">/mês</span>
                  </div>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <span className="text-green-500 mr-3">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button
                    className="w-full mt-6"
                    variant={index === 1 ? "default" : "outline"}
                    onClick={() => handlePlanSelect(plan.name)}
                  >
                    {session ? "Alterar Plano" : "Começar Agora"}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h4 className="text-2xl font-bold mb-4">🚀 Delivery SaaS</h4>
          <p className="text-gray-400 mb-8">
            Transformando negócios locais em plataformas de delivery de sucesso
          </p>
          <div className="flex justify-center space-x-6">
            <a href="#" className="text-gray-400 hover:text-white">Termos</a>
            <a href="#" className="text-gray-400 hover:text-white">Privacidade</a>
            <a href="#" className="text-gray-400 hover:text-white">Suporte</a>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-800">
            <p className="text-gray-400">
              © 2024 Delivery SaaS. Todos os direitos reservados.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}