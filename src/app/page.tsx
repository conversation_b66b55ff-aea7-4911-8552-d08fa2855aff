import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"

export default function Home() {
  const plans = [
    {
      name: "Starter",
      price: 19.90,
      description: "Perfeito para começar seu negócio",
      features: [
        "10 produtos",
        "500 pedidos/mês",
        "Suporte básico",
        "Dashboard básico"
      ]
    },
    {
      name: "Smart",
      price: 49.90,
      description: "Para negócios em crescimento",
      features: [
        "30 produtos",
        "1.500 pedidos/mês",
        "Suporte prioritário",
        "Analytics avançado",
        "Integração WhatsApp"
      ]
    },
    {
      name: "Premium",
      price: 99.90,
      description: "Para empresas estabelecidas",
      features: [
        "100 produtos",
        "Pedidos ilimitados",
        "Suporte 24/7",
        "Analytics completo",
        "Integração WhatsApp",
        "API personalizada",
        "Relatórios avançados"
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                🚀 Delivery SaaS
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">Entrar</Button>
              <Button>Começar Grátis</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Transforme seu negócio em uma
            <span className="text-blue-600"> plataforma de delivery</span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Sistema completo de delivery multitenancy com pedidos via WhatsApp,
            rastreamento em tempo real, gamificação e muito mais.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8 py-4">
              Começar Agora - Grátis
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-4">
              Ver Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Funcionalidades Diferenciais
            </h3>
            <p className="text-xl text-gray-600">
              Tudo que você precisa para dominar o mercado de delivery
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📱",
                title: "Pedidos via WhatsApp",
                description: "Gere links de checkout direto para WhatsApp"
              },
              {
                icon: "🗺️",
                title: "Rastreamento Real-time",
                description: "Acompanhe entregadores como no Uber"
              },
              {
                icon: "🎮",
                title: "Gamificação",
                description: "Selos, desafios e rankings para engajar"
              },
              {
                icon: "🌱",
                title: "Impacto Social",
                description: "Dashboard mostra negócios locais apoiados"
              },
              {
                icon: "🤖",
                title: "IA Integrada",
                description: "Atendimento automatizado inteligente"
              },
              {
                icon: "📊",
                title: "Analytics Avançado",
                description: "Insights e sugestões automáticas"
              }
            ].map((feature, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
