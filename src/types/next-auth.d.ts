import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      role: string
      avatar?: string
      tenant?: any
      customer?: any
      delivery?: any
    }
  }

  interface User {
    id: string
    email: string
    name: string
    role: string
    avatar?: string
    tenant?: any
    customer?: any
    delivery?: any
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    tenant?: any
    customer?: any
    delivery?: any
  }
}
