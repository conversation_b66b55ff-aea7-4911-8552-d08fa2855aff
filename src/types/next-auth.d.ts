import { User<PERSON><PERSON> } from "@prisma/client"
import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      role: UserRole
      avatar?: string
      tenant?: any
      customer?: any
      delivery?: any
    }
  }

  interface User {
    id: string
    email: string
    name: string
    role: UserRole
    avatar?: string
    tenant?: any
    customer?: any
    delivery?: any
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: UserRole
    tenant?: any
    customer?: any
    delivery?: any
  }
}
