import NextAuth from "next-auth"
import { PrismaAdapter } from "@auth/prisma-adapter"
import CredentialsProvider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { prisma } from "./prisma"
// import { UserRole } from "@prisma/client"

export const { handlers, auth, signIn, signOut } = NextAuth({
  // adapter: PrismaAdapter(prisma) as any, // Desabilitado para demo
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Usuários de demonstração
        const demoUsers = {
          "<EMAIL>": {
            id: "admin-1",
            email: "<EMAIL>",
            name: "<PERSON><PERSON>",
            role: "ADMIN",
            avatar: null,
            tenant: null,
            customer: null,
            delivery: null
          },
          "<EMAIL>": {
            id: "tenant-1",
            email: "<EMAIL>",
            name: "<PERSON>",
            role: "TENANT",
            avatar: null,
            tenant: { id: "tenant-1", businessName: "Pizzaria do João" },
            customer: null,
            delivery: null
          },
          "<EMAIL>": {
            id: "customer-1",
            email: "<EMAIL>",
            name: "Maria Santos",
            role: "CUSTOMER",
            avatar: null,
            tenant: null,
            customer: { id: "customer-1" },
            delivery: null
          },
          "<EMAIL>": {
            id: "delivery-1",
            email: "<EMAIL>",
            name: "Carlos Oliveira",
            role: "DELIVERY",
            avatar: null,
            tenant: null,
            customer: null,
            delivery: { id: "delivery-1", rating: 4.8 }
          }
        }

        const user = demoUsers[credentials.email as keyof typeof demoUsers]

        if (!user) {
          return null
        }

        return user
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.tenant = user.tenant
        token.customer = user.customer
        token.delivery = user.delivery
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
        session.user.tenant = token.tenant as any
        session.user.customer = token.customer as any
        session.user.delivery = token.delivery as any
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup"
  }
})
